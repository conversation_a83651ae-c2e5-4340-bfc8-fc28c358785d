{"name": "esbuild", "version": "0.21.3", "description": "An extremely fast JavaScript and CSS bundler and minifier.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "scripts": {"postinstall": "node install.js"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "bin": {"esbuild": "bin/esbuild"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.21.3", "@esbuild/android-arm": "0.21.3", "@esbuild/android-arm64": "0.21.3", "@esbuild/android-x64": "0.21.3", "@esbuild/darwin-arm64": "0.21.3", "@esbuild/darwin-x64": "0.21.3", "@esbuild/freebsd-arm64": "0.21.3", "@esbuild/freebsd-x64": "0.21.3", "@esbuild/linux-arm": "0.21.3", "@esbuild/linux-arm64": "0.21.3", "@esbuild/linux-ia32": "0.21.3", "@esbuild/linux-loong64": "0.21.3", "@esbuild/linux-mips64el": "0.21.3", "@esbuild/linux-ppc64": "0.21.3", "@esbuild/linux-riscv64": "0.21.3", "@esbuild/linux-s390x": "0.21.3", "@esbuild/linux-x64": "0.21.3", "@esbuild/netbsd-x64": "0.21.3", "@esbuild/openbsd-x64": "0.21.3", "@esbuild/sunos-x64": "0.21.3", "@esbuild/win32-arm64": "0.21.3", "@esbuild/win32-ia32": "0.21.3", "@esbuild/win32-x64": "0.21.3"}, "license": "MIT"}