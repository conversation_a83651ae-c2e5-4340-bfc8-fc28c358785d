{"name": "@angular-devkit/core", "version": "18.0.4", "description": "Angular DevKit - Core Utility Library", "main": "src/index.js", "typings": "src/index.d.ts", "keywords": ["Angular CLI", "Angular DevKit", "angular", "core", "devkit", "sdk"], "exports": {".": {"types": "./src/index.d.ts", "default": "./src/index.js"}, "./node": {"types": "./node/index.d.ts", "default": "./node/index.js"}, "./node/testing": {"types": "./node/testing/index.d.ts", "default": "./node/testing/index.js"}, "./package.json": "./package.json", "./*": "./*.js", "./*.js": "./*.js"}, "dependencies": {"ajv-formats": "3.0.1", "ajv": "8.13.0", "jsonc-parser": "3.2.1", "picomatch": "4.0.2", "rxjs": "7.8.1", "source-map": "0.7.4"}, "peerDependencies": {"chokidar": "^3.5.2"}, "peerDependenciesMeta": {"chokidar": {"optional": true}}, "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli"}