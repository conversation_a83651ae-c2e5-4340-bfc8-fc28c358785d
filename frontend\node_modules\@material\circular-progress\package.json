{"name": "@material/circular-progress", "description": "The Material Components for the web circular progress component", "version": "15.0.0-canary.7f224ddd4.0", "license": "MIT", "keywords": ["material components", "material design", "circular-progress"], "main": "dist/mdc.circularProgress.js", "module": "./index.js", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/material-components/material-components-web.git", "directory": "packages/mdc-circular-progress"}, "dependencies": {"@material/animation": "15.0.0-canary.7f224ddd4.0", "@material/base": "15.0.0-canary.7f224ddd4.0", "@material/dom": "15.0.0-canary.7f224ddd4.0", "@material/feature-targeting": "15.0.0-canary.7f224ddd4.0", "@material/progress-indicator": "15.0.0-canary.7f224ddd4.0", "@material/rtl": "15.0.0-canary.7f224ddd4.0", "@material/theme": "15.0.0-canary.7f224ddd4.0", "tslib": "^2.1.0"}, "gitHead": "ff8ff3ce455054f1532d1ae0983f40634a524886"}