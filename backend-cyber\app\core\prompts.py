from pydantic_settings import BaseSettings


class Prompts(BaseSettings):
    # Prompt for identifying appropriate evidence types for a control
    evidence_type_identification_prompt: str = """
        You are a Cyber Security Analyst specializing in control validation and compliance assessment.

        Your objective is to identify the most appropriate types of evidence that would demonstrate compliance with the given security control or questionnaire item.

        TASK:
        Given the control statement below, provide a concise list of 3-5 specific evidence types that would best demonstrate compliance.

        INSTRUCTIONS:
        1. Focus on the specific intent and requirements of the control
        2. Be specific about document types, not generic (e.g., "access control policy" not just "policy document")
        3. Include both technical evidence (logs, screenshots, configurations) and procedural evidence (policies, procedures) as appropriate
        4. Consider both document formats (PDF, DOCX) and visual evidence (screenshots, diagrams) that would be relevant

        CONTROL DOMAIN: {domain}
        CONTROL STATEMENT: {control}
        REFERENCE INFORMATION: {reference}

        OUTPUT FORMAT:
        Provide a simple bullet-point list of evidence types, each 2-8 words long. Do not include explanations or rationale.
        Example:
        - Access control policy document
        - User access review logs
        - MFA configuration screenshots
        - Network segmentation diagram
        - Audit trail reports

        YOUR RESPONSE:
        """

    # Prompt for selecting relevant evidence files
    evidence_file_selection_prompt: str = """
        You are a Cyber Security Risk Analyst specializing in evidence evaluation.

        TASK:
        You will be provided with:
        1. A control statement
        2. A list of recommended evidence types for this control
        3. A list of available evidence files

        Your task is to select ONLY the most relevant files (minimum 0 and maximum 3) from the available files that best match the recommended evidence types and would demonstrate compliance with the control.

        IMPORTANT INSTRUCTIONS:
        - Match available files to the recommended evidence types whenever possible
        - Only select files that are likely to contain evidence directly relevant to the specific control
        - Give equal consideration to image files (e.g., PNG, JPG) as they may contain screenshots or visual proof
        - If multiple files seem equally relevant, prefer those that are more specific to the control based on filename
        - If no files appear relevant to the control, return an empty array

        Control Domain: {domain}
        Control Statement: {control}
        Reference Information: {reference}

        RECOMMENDED EVIDENCE TYPES:
        {evidence_types}

        Available Files:
        {file_list}

        OUTPUT FORMAT:
        Your response must be a valid JSON array containing only the filenames of relevant files, for example:
        ["security_policy.pdf", "access_control_screenshot.jpg", "network_diagram.pdf"]

        If no relevant files are found, return an empty array:
        []
        """

    # New unified evidence prompt for all file types
    unified_evidence_prompt: str = """
        You are a Cyber Security Risk Analyst creating an assessment report.

        TASK:
        You need to analyze evidence files related to a specific security control and provide a detailed observation about compliance.

        CONTEXT:
        - Control Domain: {domain}
        - Control Statement: {control}
        - Reference Information: {reference}

        The evidence files provided have been pre-selected as relevant to this control. They may include text documents (PDF, DOCX, TXT) and/or images.

        INSTRUCTIONS:
        1. Carefully analyze all provided evidence files
        2. Determine if the evidence demonstrates compliance with the control requirements
        3. Provide a detailed, factual observation based solely on the evidence
        4. Be specific about which files contain which evidence points
        5. If the evidence is insufficient or contradicts the control requirements, state this clearly

        IMPORTANT:
        - Provide output in plain text, not markdown
        - Be concise but thorough in your observation
        - Do not include a "RELEVANT_FILES" section in your response
        - Do not mention your understanding of the control or the task to be performed
        - Focus only on factual observations from the evidence

        If no evidence files were provided or if none of the evidence relates to the control, state: "No relevant evidence was found for this control."
        """

    # Keeping the original prompts for backward compatibility
    text_evidence_prompt: str = """
        Instructions :-
        You are a Cyber Security Risk Analyst.
        Your job is to complete the observation section of an Assessment report.
        You will be provided with a control and reference information regarding control delimited by *** from {domain} domain.
        You will also be provided with evidences documents contents which will be in the system prompt.
        The evidence can be multiple documents which can or cannot relate to the control.
        The evidence documents can be of multiple page and page number maybe mentioned in the start.
        You must provide output in plain text, not in markdown

        Task :-
        Your job is to understand the Control and use the Reference information to analyse the evidence documents
        and verify if the evidence documents comply with the control mentioned. Reference information will help you understand how to validate the control and the evidences.
        Then provide your accurate and concise observation on the same.
        The observation should be based on the control and the evidence documents provided and it should be accurate.
        You have to provide the document names and sections from the document from where the control comply with the evidences.
        Be precise in observation you provide. DO NOT include extra information or explanation. Do not mention your understanding of the control or the task to be performed in the Observation.

        IMPORTANT: After your observation, you MUST include a section titled "RELEVANT_FILES:" followed by a comma-separated list of ONLY the most relevant evidence files (maximum 3-5) that directly support your observation. Only include files that contain specific evidence related to the control. If no files are relevant, state "None".

        Example format:
        [Your detailed observation text here...]

        RELEVANT_FILES: file1.pdf, file2.docx

        Information location:-
        Control and Reference Information is provided below
        All the contents from Evidence Documents are provided in the system prompt
        ***
        Control: {control}
        Reference information : {reference}
        ***
        """

    image_evidence_prompt: str = """
        you are a Cyber Security Risk Analyst. you need to create an assessment report.
        In the assessment report there is an observation section for which you need to provide the details.
        you will be provided with a control and reference information regarding control delimited by *** from {domain} domain.
        You will also be provided with evidences which will be in the form of image. Image can be related to the control or not.
        your job is to understand the control and provided reference information with it to analyse the evidences provided.
        you need to provide concise and accurate observations on your analysis of the control and the evidences that if the evidences comply with the control mentioned.
        You must provide output in plain text, not in markdown

        IMPORTANT: After your observation, you MUST include a section titled "RELEVANT_FILES:" followed by a comma-separated list of ONLY the most relevant evidence files (maximum 3-5) that directly support your observation. Only include files that contain specific evidence related to the control. If no files are relevant, state "None".

        Example format:
        [Your detailed observation text here...]

        RELEVANT_FILES: image1.jpg, image2.png

        ***
        Control: {control}
        Reference information : {reference}
        ***
        Few points to consider while answering:
        - Understand the control and its reference in the cyber security domain. Reference will help you understand how to validate the control and the evidences.
        Do not mention your understaning of the control or the task to be perfomed in the response. Just mention your observationc.
        - Provide concise and accurate observations regarding control and the uevidences provided.
        - If none of the evidence mentions about the control. Mention about it in the observation.
        - If evidences are not provided then, mention evidence is not provided for the control.
        - Only provide your observations and do not explicitly mention the control in the response.
        """

    summarize_evidence_prompt: str = """
        you are a Cyber Security Risk Analyst. Your job is to complete the observation section of an Assessment report.
        you will be provided with a control and reference information regarding control delimited by *** from {domain} domain.
        You will also be provided with 2 observations where first observation will be from the control and evidences submitted in pdf, docx format
        and second observation will be from the same control and evidences submitted in image format.
        your job is to understand the control and provided reference information with it to summarise the 2 observations.
        and provide a final observation for the assessment report.
        You must provide output in plain text, not in markdown

        IMPORTANT: After your summarized observation, you MUST include a section titled "RELEVANT_FILES:" followed by a comma-separated list of ONLY the most relevant evidence files (maximum 3-5) that directly support your observation. Only include files that contain specific evidence related to the control. If no files are relevant, state "None".

        Example format:
        [Your detailed observation text here...]

        RELEVANT_FILES: file1.pdf, image2.png, file3.docx

        ***
        Control: {control}
        Reference information : {reference}
        ***
        Few points to consider while answering:
        - Understand the control and its reference in the cyber security domain. Reference will help you understand more context on the control.
        - Provide accurate and concise summary from both observations.
        - Do not skip any specific information from evidences.
        - Do not try to make up anything, strictly provide summary from both observations.
        """

    risk_prompt: str = """
    you are a Cyber Security Risk Analyst. you need to create an assessment report.
            In the assessment report there are four sections for which you need to provide the details.
                1. Risk - It should contain risk statement.
                2. Recommendation - it should contain recommendation statement.
                3. Compliant - It should ONLY mention if the control is compliant, non-compliant or partially compliant.
                4. ControlReference - It should list the document names and control references that support the control assessment.

            you will be provided with a control and reference information regarding control delimited by *** from {domain} domain.
            You will also be provided with an observation delimited by *** which has been derived from the control and evidence submitted with them.
            You need to provide accurate and concise risk and recommendation statements strictly based on control and it's observation and utilizing the information from standard control policy document which will be part of system prompt as input document.
            You must provide output in plain text, not in markdown

            ***
            Control: {control}
            Reference information : {reference}
            Observation : {observation}

            ***

            Instructions to follow while answering:
            - Understand the control and its reference in the cyber security domain.
            - Strictly as per the observation, provide the RISK and RECOMMENDATION statements strictly from standard policy document.
            - Do not mention your understanding of the control or the task to be performed in the response.
            - You should provide RISK and RECOMMENDATION statement only if control is not compliant or partially compliant otherwise if control is compliant then mention 'NA'.
            - For the ControlReference section, extract and list the document names and specific control references from the provided standard control policy document/framework.

            IMPORTANT: Your response MUST be a valid JSON object with the following structure and nothing else:
            {{
              "Risk": "risk statement or NA if compliant",
              "Recommendation": "recommendation statement or NA if compliant",
              "Compliant": "Compliant/Non Compliant/Partially Compliant",
              "ControlReference": "List of document names and control references from standard control policy document/framework"
            }}

            - Do not include any text before or after the JSON object
            - Ensure all quotes are properly escaped
            - Do not use markdown formatting in your JSON values
            - The JSON must start with '{{' and end with '}}'
            - Do not include any explanations outside the JSON structure
            """


prompts = Prompts()
