{"name": "domutils", "version": "3.1.0", "description": "Utilities for working with htmlparser2's dom", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib/**/*"], "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint --ignore-path .gitignore .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier \"**/*.{ts,md,json,yml}\" --ignore-path .gitignore", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/domutils/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "build:docs": "typedoc --hideGenerator --exclude \"**/*+(index|.spec).ts\" --categorizeByGroup false --sort enum-value-ascending --sort alphabetical src", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domutils.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}, "devDependencies": {"@types/jest": "^29.5.1", "@types/node": "^18.16.2", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-jsdoc": "^43.1.1", "htmlparser2": "~8.0.2", "jest": "^29.5.0", "prettier": "^2.8.8", "ts-jest": "^29.1.0", "typedoc": "^0.24.6", "typescript": "^5.0.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": ["$1.js", "$1"]}}, "prettier": {"tabWidth": 4}}