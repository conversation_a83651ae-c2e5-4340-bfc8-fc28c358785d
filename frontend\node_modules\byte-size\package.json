{"name": "byte-size", "author": "<PERSON> <<EMAIL>>", "version": "8.1.1", "description": "Convert a bytes or octets value (e.g. 34565346) to a human-readable string ('34.6 MB'). Choose between metric or IEC units.", "repository": "https://github.com/75lb/byte-size", "type": "module", "exports": {"import": "./index.js", "require": "./dist/index.cjs"}, "license": "MIT", "engines": {"node": ">=12.17"}, "files": ["index.js", "dist"], "keywords": ["convert", "bytes", "octet", "size", "human", "readable", "metric", "IEC"], "scripts": {"test": "npm run dist && npm run test:ci", "test:ci": "npm run test:esm", "test:esm": "test-runner --view.hide-skips test.js", "docs": "jsdoc2md -t README.hbs index.js > README.md", "cover": "c8 npm test && c8 report --reporter=text-lcov | coveralls", "dist": "rollup -c"}, "devDependencies": {"jsdoc-to-markdown": "^8.0.0", "rollup": "^3.21.3", "test-runner": "^0.10.1"}, "standard": {"ignore": ["dist"]}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://repejota.com"}]}