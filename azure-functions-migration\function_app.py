import azure.functions as func
import logging
import json
import os
import base64
import uuid
import tempfile
from typing import Optional, List

# We'll import your existing modules after copying them
# from shared.evidence_main import save_evidence_files
# from shared.document_mappings import get_documents_for_region_selection, etc.

app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

@app.route(route="analyze_evidences", methods=["POST"])
def analyze_evidences(req: func.HttpRequest) -> func.HttpResponse:
    """
    Main evidence analyzer endpoint - handles Base64 files from PowerApps
    """
    try:
        logging.info("Starting evidence analysis")
        
        # Get JSON data from PowerApps
        data = req.get_json()
        if not data:
            return func.HttpResponse(
                json.dumps({"error": "No JSON data received"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Extract files (Base64 encoded from PowerApps)
        files_base64 = data.get('files', [])
        
        # Extract parameters (same as your FastAPI version)
        benchmarks = data.get('benchmarks')
        question_method = data.get('question_method')
        hardcoded_questions = data.get('hardcoded_questions')
        region_selection = data.get('region_selection')
        domain_selection = data.get('domain_selection')
        domain_evidence_mapping = data.get('domain_evidence_mapping')
        
        logging.info(f"Received {len(files_base64)} files for processing")
        
        # Convert Base64 files to temporary files
        temp_files = []
        
        for i, file_data in enumerate(files_base64):
            file_name = file_data.get('name', f'file_{i}.pdf')
            file_content_base64 = file_data.get('content')
            
            if file_content_base64:
                try:
                    # Remove data URL prefix if present (data:application/pdf;base64,)
                    if ',' in file_content_base64:
                        file_content_base64 = file_content_base64.split(',')[1]
                    
                    # Decode Base64 to bytes
                    file_bytes = base64.b64decode(file_content_base64)
                    
                    # Create temp directory if it doesn't exist
                    temp_dir = "/tmp"
                    os.makedirs(temp_dir, exist_ok=True)
                    
                    # Save to temporary file
                    temp_file_path = os.path.join(temp_dir, file_name)
                    with open(temp_file_path, "wb") as f:
                        f.write(file_bytes)
                    
                    temp_files.append({
                        'path': temp_file_path,
                        'name': file_name,
                        'size': len(file_bytes)
                    })
                    
                    logging.info(f"Processed file: {file_name}, size: {len(file_bytes)} bytes")
                    
                except Exception as e:
                    logging.error(f"Error processing file {file_name}: {str(e)}")
                    continue
        
        # Parse JSON strings (same logic as your FastAPI)
        selected_benchmarks = None
        if benchmarks:
            try:
                selected_benchmarks = json.loads(benchmarks) if isinstance(benchmarks, str) else benchmarks
            except:
                selected_benchmarks = None
        
        region_data = None
        if region_selection:
            try:
                region_data = json.loads(region_selection) if isinstance(region_selection, str) else region_selection
            except:
                region_data = None
        
        domain_data = None
        if domain_selection:
            try:
                domain_data = json.loads(domain_selection) if isinstance(domain_selection, str) else domain_selection
            except:
                domain_data = None
        
        # Create job ID for tracking
        job_id = str(uuid.uuid4())
        
        # TODO: Here we'll call your existing evidence analysis logic
        # For now, return success with job tracking
        
        return func.HttpResponse(
            json.dumps({
                "job_id": job_id,
                "status": "queued",
                "message": f"Analysis job queued successfully. {len(temp_files)} files processed.",
                "files_processed": len(temp_files)
            }),
            status_code=202,
            mimetype="application/json"
        )
        
    except Exception as e:
        logging.error(f"Error in analyze_evidences: {str(e)}")
        import traceback
        traceback.print_exc()
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )

@app.route(route="region_selection", methods=["GET"])
def get_region_selection(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get region selection options - will copy from your FastAPI code
    """
    try:
        # TODO: Import and use your existing logic
        # For now, return sample data
        regions = ["North America", "Europe", "Asia Pacific"]
        industries_by_region = {
            "North America": ["Technology", "Healthcare", "Finance"],
            "Europe": ["Technology", "Manufacturing", "Finance"],
            "Asia Pacific": ["Technology", "Manufacturing", "Retail"]
        }
        sectors_by_industry = {
            "North America_Technology": ["Software", "Hardware", "Cloud"],
            "Europe_Technology": ["Software", "Hardware", "Telecom"],
        }
        
        return func.HttpResponse(
            json.dumps({
                "regions": regions,
                "industries_by_region": industries_by_region,
                "sectors_by_industry": sectors_by_industry
            }),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        logging.error(f"Error in get_region_selection: {str(e)}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )

@app.route(route="domain_selection", methods=["GET"])
def get_domain_selection(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get domain selection options - will copy from your FastAPI code
    """
    try:
        # TODO: Import and use your existing logic
        domains = ["Cloud Security", "Data Protection", "Identity Management"]
        csps_by_domain = {
            "Cloud Security": ["AWS", "Azure", "GCP"],
            "Data Protection": ["General"],
            "Identity Management": ["General"]
        }
        
        return func.HttpResponse(
            json.dumps({
                "domains": domains,
                "csps_by_domain": csps_by_domain
            }),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        logging.error(f"Error in get_domain_selection: {str(e)}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )

@app.route(route="job_status/{job_id}", methods=["GET"])
def get_job_status(req: func.HttpRequest) -> func.HttpResponse:
    """
    Check the status of an analysis job
    """
    try:
        job_id = req.route_params.get('job_id')
        
        # TODO: Implement actual job status tracking
        # For now, return sample status
        status = {
            "job_id": job_id,
            "status": "completed",
            "progress": 100,
            "message": "Analysis completed successfully",
            "result": {
                "processed_questions": 10,
                "evidence_files": 5,
                "analysis_complete": True
            }
        }
        
        return func.HttpResponse(
            json.dumps(status),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        logging.error(f"Error in get_job_status: {str(e)}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )

@app.route(route="health", methods=["GET"])
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    Health check endpoint
    """
    return func.HttpResponse(
        json.dumps({
            "status": "healthy",
            "service": "IsecMapper Evidence Analyzer",
            "version": "1.0.0"
        }),
        status_code=200,
        mimetype="application/json"
    )
