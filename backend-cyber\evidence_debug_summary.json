[{"control_id": "1", "domain": "Information Security", "control": "Do you have an information security awareness program which include mandatory trainings to employees for below security topics? \n- Ransomware/emerging threats\n- Clean Desk and Laptop Security Guidelines\n", "evidence_files_considered": ["1_ISAT.pdf", "6 - 1.pdf", "6 - 2.pdf", "10 - 1.docx", "18 - 1.pdf", "50 - 1.pdf", "51.pdf", "2_settings - 1.png", "2_settings- 2.png", "10 - 2.png", "18 - 2.png", "50 - 2.png"]}, {"control_id": "2", "domain": "Information Security", "control": "Do you restrict user from installing unauthorized or unlicensed software?", "evidence_files_considered": ["1_ISAT.pdf", "6 - 1.pdf", "6 - 2.pdf", "10 - 1.docx", "18 - 1.pdf", "50 - 1.pdf", "51.pdf", "2_settings - 1.png", "2_settings- 2.png", "10 - 2.png", "18 - 2.png", "50 - 2.png"]}, {"control_id": "3", "domain": "Physical", "control": "Do you implement multi-tier network architecture (i.e. use of demilitarized zone - DMZ) to segment public-facing systems from back-end systems?", "evidence_files_considered": ["33.pdf", "3_network diagram.PNG"]}, {"control_id": "4", "domain": "People", "control": "Are your employee subjected to background investigation checks including below aspects?\n- Identity verification\n- Educational background verification\n- Employment/professional background verification\n- Criminal/police record check\n", "evidence_files_considered": ["4_Background Verification Document.pdf", "4_SHRM-<PERSON><PERSON>-Background-Check-Policy.pdf"]}, {"control_id": "18", "domain": "Information Security", "control": "Are you using encryption to protect data at-rest (AES256 or higher) and data in-transit (TLS V1.2 or higher)?\n\nIf yes, please specify the encryption algorithm in \"Third Party Remark\"", "evidence_files_considered": ["1_ISAT.pdf", "6 - 1.pdf", "6 - 2.pdf", "10 - 1.docx", "18 - 1.pdf", "50 - 1.pdf", "51.pdf", "2_settings - 1.png", "2_settings- 2.png", "10 - 2.png", "18 - 2.png", "50 - 2.png"]}, {"control_id": "43", "domain": "Technological", "control": "Does your cloud service provider have BYOK capability to secure VM, data, authentication by using XXX Client supplied private key?\n\nIf yes, please specify what format of the encryption keys are supported in \"Third Party Remark\".", "evidence_files_considered": ["7.pdf", "9_Mobile Device Management Policy.pdf", "11_MFA Policy - 1.pdf", "16_Data Encryption Policy.pdf", "45 - 5.pdf", "8 - 1.png", "8 - 2.png", "11_Access Control - 2.png", "11_Enable Policy - 4.png", "11_MFA Request - 3.png", "29.jpg", "43.png", "45 - 1.png", "45 - 2.png", "45 - 3.png", "45 - 4.png"]}, {"control_id": "50", "domain": "Information Security", "control": "Do you perform data backup for the system/application that include XXX Client data?", "evidence_files_considered": ["1_ISAT.pdf", "6 - 1.pdf", "6 - 2.pdf", "10 - 1.docx", "18 - 1.pdf", "50 - 1.pdf", "51.pdf", "2_settings - 1.png", "2_settings- 2.png", "10 - 2.png", "18 - 2.png", "50 - 2.png"]}, {"control_id": "51", "domain": "Information Security", "control": "Are independent security audits performed to your organization?", "evidence_files_considered": ["1_ISAT.pdf", "6 - 1.pdf", "6 - 2.pdf", "10 - 1.docx", "18 - 1.pdf", "50 - 1.pdf", "51.pdf", "2_settings - 1.png", "2_settings- 2.png", "10 - 2.png", "18 - 2.png", "50 - 2.png"]}, {"control_id": "6", "domain": "Information Security", "control": "Do you have an IT asset management policy which covers below mentioned points for hardware, software and information assets (e.g., databases):\n- Maintaining asset inventory\n- Asset ownership\n- Classification of Assets \n- Acceptable use of Assets\n- Process to perform periodic recertification of assets\n", "evidence_files_considered": ["1_ISAT.pdf", "6 - 1.pdf", "6 - 2.pdf", "10 - 1.docx", "18 - 1.pdf", "50 - 1.pdf", "51.pdf", "2_settings - 1.png", "2_settings- 2.png", "10 - 2.png", "18 - 2.png", "50 - 2.png"]}, {"control_id": "7", "domain": "Technological", "control": "Do you define data classification for information assets and control requirement for data protection accordingly?", "evidence_files_considered": ["7.pdf", "9_Mobile Device Management Policy.pdf", "11_MFA Policy - 1.pdf", "16_Data Encryption Policy.pdf", "45 - 5.pdf", "8 - 1.png", "8 - 2.png", "11_Access Control - 2.png", "11_Enable Policy - 4.png", "11_MFA Request - 3.png", "29.jpg", "43.png", "45 - 1.png", "45 - 2.png", "45 - 3.png", "45 - 4.png"]}, {"control_id": "8", "domain": "Technological", "control": "Do you restrict user from installing unauthorized or unlicensed software?", "evidence_files_considered": ["7.pdf", "9_Mobile Device Management Policy.pdf", "11_MFA Policy - 1.pdf", "16_Data Encryption Policy.pdf", "45 - 5.pdf", "8 - 1.png", "8 - 2.png", "11_Access Control - 2.png", "11_Enable Policy - 4.png", "11_MFA Request - 3.png", "29.jpg", "43.png", "45 - 1.png", "45 - 2.png", "45 - 3.png", "45 - 4.png"]}, {"control_id": "9", "domain": "Technological", "control": "Do you have mobile device management (MDM) policy which require to enforce below security controls to the devices?\n- Data encryption (full disk encryption or volume-level encryption)\n- Anti-virus\n- Remote wipe (for Mobiles)", "evidence_files_considered": ["7.pdf", "9_Mobile Device Management Policy.pdf", "11_MFA Policy - 1.pdf", "16_Data Encryption Policy.pdf", "45 - 5.pdf", "8 - 1.png", "8 - 2.png", "11_Access Control - 2.png", "11_Enable Policy - 4.png", "11_MFA Request - 3.png", "29.jpg", "43.png", "45 - 1.png", "45 - 2.png", "45 - 3.png", "45 - 4.png"]}, {"control_id": "10", "domain": "Information Security", "control": "Do you have an access control policy that covers below requirements?\n1. Users are uniquely identified during authentication process\n2. Principles of least privilege, segregation of duties (SoD) and need-to-know followed during user authorization\n3. Policies and procedures for removal of access for employees who are terminated or transferred. \n4. The validity of user account is performed periodically.", "evidence_files_considered": ["1_ISAT.pdf", "6 - 1.pdf", "6 - 2.pdf", "10 - 1.docx", "18 - 1.pdf", "50 - 1.pdf", "51.pdf", "2_settings - 1.png", "2_settings- 2.png", "10 - 2.png", "18 - 2.png", "50 - 2.png"]}, {"control_id": "11", "domain": "Technological", "control": "Do you enforce MFA(Multi-factor authentication) for the below platforms?\n\n1) Public-facing systems/applications hosting and/or processing/transmitting XXX Client sensitive data (e.g. customer records, PII, etc.)\n2) In VPN solutions for secure remote access\n3) Cloud environment\n4) Privilege access\n\n\n", "evidence_files_considered": ["7.pdf", "9_Mobile Device Management Policy.pdf", "11_MFA Policy - 1.pdf", "16_Data Encryption Policy.pdf", "45 - 5.pdf", "8 - 1.png", "8 - 2.png", "11_Access Control - 2.png", "11_Enable Policy - 4.png", "11_MFA Request - 3.png", "29.jpg", "43.png", "45 - 1.png", "45 - 2.png", "45 - 3.png", "45 - 4.png"]}, {"control_id": "16", "domain": "Technological", "control": "Whether passwords stored are hashed using one of below hashing algorithms?\n- Argon2id\n- bcrypt\n- PBKDF2\n- SHA512", "evidence_files_considered": ["7.pdf", "9_Mobile Device Management Policy.pdf", "11_MFA Policy - 1.pdf", "16_Data Encryption Policy.pdf", "45 - 5.pdf", "8 - 1.png", "8 - 2.png", "11_Access Control - 2.png", "11_Enable Policy - 4.png", "11_MFA Request - 3.png", "29.jpg", "43.png", "45 - 1.png", "45 - 2.png", "45 - 3.png", "45 - 4.png"]}, {"control_id": "29", "domain": "Technological", "control": "Do you establish DLP rulesets in the DLP solution you used to protect below XXX Client senstiive information?\n- Personal Identifiable Info (PII) - e.g national ID, passport ID, mobile number etc.\n- Payment Card Info (PCI) - e.g credit card number, bank account number etc.\n- Insurance Policy Info - e.g insurance policy number\n\n", "evidence_files_considered": ["7.pdf", "9_Mobile Device Management Policy.pdf", "11_MFA Policy - 1.pdf", "16_Data Encryption Policy.pdf", "45 - 5.pdf", "8 - 1.png", "8 - 2.png", "11_Access Control - 2.png", "11_Enable Policy - 4.png", "11_MFA Request - 3.png", "29.jpg", "43.png", "45 - 1.png", "45 - 2.png", "45 - 3.png", "45 - 4.png"]}, {"control_id": "33", "domain": "Physical", "control": "Do you enforce below controls to protect the established wireless network which handle XXX Client data?\n- Use of WPA2 to enforce both authentication and encryption\n- Detection of wireless threats (e.g. wireless IDS/IPS, wireless network vulnerability assessment or penetration test, etc.)\n", "evidence_files_considered": ["33.pdf", "3_network diagram.PNG"]}, {"control_id": "45", "domain": "Technological", "control": "Does your cloud service provider have capabilities to detect anomalous network traffic and user behaviour?", "evidence_files_considered": ["7.pdf", "9_Mobile Device Management Policy.pdf", "11_MFA Policy - 1.pdf", "16_Data Encryption Policy.pdf", "45 - 5.pdf", "8 - 1.png", "8 - 2.png", "11_Access Control - 2.png", "11_Enable Policy - 4.png", "11_MFA Request - 3.png", "29.jpg", "43.png", "45 - 1.png", "45 - 2.png", "45 - 3.png", "45 - 4.png"]}]