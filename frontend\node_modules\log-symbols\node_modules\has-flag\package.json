{"name": "has-flag", "version": "4.0.0", "description": "Check if argv has a specific flag", "license": "MIT", "repository": "sindresorhus/has-flag", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}