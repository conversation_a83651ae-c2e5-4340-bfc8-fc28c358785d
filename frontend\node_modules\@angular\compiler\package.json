{"name": "@angular/compiler", "version": "18.0.3", "description": "Angular - the compiler library", "author": "angular", "license": "MIT", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": "18.0.3"}, "peerDependenciesMeta": {"@angular/core": {"optional": true}}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/compiler"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": true, "module": "./fesm2022/compiler.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/index.mjs", "esm": "./esm2022/index.mjs", "default": "./fesm2022/compiler.mjs"}}}