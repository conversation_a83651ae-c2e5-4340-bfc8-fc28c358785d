import os
import time
from typing import List
from openai import AzureOpenAI
from langchain_openai import AzureChatOpenAI
from langchain_openai import AzureOpenAIEmbeddings
from llama_index.llms.azure_openai import AzureOpenAI as llama_AzureOpenAI
from llama_index.embeddings.azure_openai import (
AzureOpenAIEmbedding as llama_AzureOpenAIEmbedding,
)
from langchain_community.vectorstores import FAISS


class AzureClientManager:
    """
    Manages multiple Azure OpenAI clients and rotates between them to avoid rate limits.
    """
    def __init__(self, api_key, api_version, base_endpoint, deployment_names):
        self.clients = []
        self.current_index = 0

        # Create a client for each deployment name
        for deployment_name in deployment_names:
            # Construct the base URL with the specific deployment name
            base_url = f"{base_endpoint}/openai/deployments/{deployment_name}/chat/completions?api-version={api_version}"

            # Create the client
            client = AzureOpenAI(
                api_key=api_key,
                api_version=api_version,
                base_url=base_url,
            )

            # Add to the list of clients
            self.clients.append(client)

        print(f"Initialized {len(self.clients)} Azure OpenAI clients")

    def get_next_client(self):
        """Get the next client in the rotation"""
        client = self.clients[self.current_index]
        # Rotate to the next client for the next call
        self.current_index = (self.current_index + 1) % len(self.clients)
        return client

class Configs:
    # Base
    PROJECT_NAME: str = "IsecMapper"
    PROJECT_VERSION: str = "v1.5-stable"

    API: str = "/api"
    API_V1_STR: str = "/api"

    API: str = "/api"
    API_V1_STR: str = "/api"

    # CORS
    BACKEND_CORS_ORIGINS: List[str] = ["*"]

    # Storage location for chatbot files
    curr_dir: str = os.path.join(os.path.dirname(os.path.realpath(__file__)), "..")
    TEMP_CONSTANT: str = "temp_storage"
    CHATBOT_TEMP_FILE_PATH: str = "chatbot_file"
    LLAMA_INDEX_VECTOR_STORE: str = "storage"
    EVIDENCE_DOWNLOAD_TEMP_STORAGE: str = "evidence_download_storage"
    EVIDENCE_ANALYZER_TEMP_STORAGE: str = "evidence_analyzer_storage"

    permanent_storage: str = "permanent_storage"
    POLICY_FILE_PATH: str = "policy_document"
    FAISS_INDEX_STORE: str = "vector_DB"
    index_name: str = "FAISS_IsecMapper_standards"

    thread_pool_size: int = 20  # Increased from 8 to handle more parallel processing
    rate_limit_per_min: int = 360
    delay_in_seconds: float = 60.0 / rate_limit_per_min

    # --------------------------------------------------------------------
    # Azure OpenAI configuration
    # MSP_AZURE_OPENAI_VERSION = "2025-01-01-preview"
    # MSP_AZURE_OPENAI_ENDPOINT = "https://test-openai-test.openai.azure.com"
    # MSP_AZURE_OPENAI_KEY = "********************************"

    
    MSP_AZURE_OPENAI_VERSION = "2025-01-01-preview"
    MSP_AZURE_OPENAI_ENDPOINT = "https://aus-openai-am.openai.azure.com/"
    MSP_AZURE_OPENAI_KEY = "********************************"

    # List of all GPT-4.1 deployment names
    GPT4_DEPLOYMENT_NAMES = [
        "gpt-4.1",
        "gpt-4.1-2",
        "gpt-4.1-3",
        "gpt-4.1-4",
        "gpt-4.1-5",
        "gpt-4.1-6"
    ]

    # Default deployment names for backward compatibility
    MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME = "gpt-4.1"
    MSP_AZURE_GPT4TURBO_MODEL_NAME = "gpt-4.1"

    MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME = "gpt-4.1"
    MSP_AZURE_GPT4VISION_MODEL_NAME = "gpt-4.1"

    MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME = "embd_test"
    MSP_AZURE_EMBEDDING_MODEL_NAME = "text-embedding-ada-002"
    # --------------------------------------------------------------------

    # Initialize the client manager with all 6 deployments
    azure_client_manager = AzureClientManager(
        api_key=MSP_AZURE_OPENAI_KEY,
        api_version=MSP_AZURE_OPENAI_VERSION,
        base_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
        deployment_names=GPT4_DEPLOYMENT_NAMES
    )

    # For backward compatibility, keep the original clients
    Azure_client_GPT4_Vision = AzureOpenAI(
        api_key=MSP_AZURE_OPENAI_KEY,
        api_version=MSP_AZURE_OPENAI_VERSION,
        base_url=f"{MSP_AZURE_OPENAI_ENDPOINT}/openai/deployments/{MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME}/chat/completions?api-version={MSP_AZURE_OPENAI_VERSION}",
    )

    Azure_client_GPT4_Turbo = AzureOpenAI(
        api_key=MSP_AZURE_OPENAI_KEY,
        api_version=MSP_AZURE_OPENAI_VERSION,
        base_url=f"{MSP_AZURE_OPENAI_ENDPOINT}/openai/deployments/{MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME}/chat/completions?api-version={MSP_AZURE_OPENAI_VERSION}",
    )

    Azure_LangChain_client_GPT4_Turbo = AzureChatOpenAI(
        base_url=f"{MSP_AZURE_OPENAI_ENDPOINT}/openai/deployments/{MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME}/chat/completions?api-version={MSP_AZURE_OPENAI_VERSION}",
        api_key=MSP_AZURE_OPENAI_KEY,
        model_name="gpt-4.1",
        api_version=MSP_AZURE_OPENAI_VERSION,
        # temperature=0,
    )

    Azure_langchain_client_embedding = AzureOpenAIEmbeddings(
        azure_deployment=MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME,
        azure_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
        api_key=MSP_AZURE_OPENAI_KEY,
        show_progress_bar=True,
    )

    aoai_client = {
        "api_key": MSP_AZURE_OPENAI_KEY,
        "api_version": MSP_AZURE_OPENAI_VERSION,
        "azure_endpoint": MSP_AZURE_OPENAI_ENDPOINT,
        "azure_deployment": MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME,
        "model": MSP_AZURE_GPT4TURBO_MODEL_NAME,
    }

    aoai_embedding = {
        "api_key": MSP_AZURE_OPENAI_KEY,
        "api_version": "2023-05-15",
        "azure_endpoint": MSP_AZURE_OPENAI_ENDPOINT,
        "azure_deployment": MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME,
        "model": MSP_AZURE_EMBEDDING_MODEL_NAME,
    }

    # Method to get the next available client from the rotation
    @classmethod
    def get_next_azure_client(cls):
        """
        Get the next available Azure OpenAI client from the rotation.
        This helps distribute API calls across multiple deployments to avoid rate limits.

        Returns:
            An AzureOpenAI client instance
        """
        return cls.azure_client_manager.get_next_client()

    llama_aoai_chat = llama_AzureOpenAI(**aoai_client)
    llama_aoai_embedding = llama_AzureOpenAIEmbedding(**aoai_embedding)

    # Local Faiss store
    db_folder_path = os.path.join(curr_dir, permanent_storage, FAISS_INDEX_STORE)
    local_faiss_store = FAISS.load_local(
        folder_path=db_folder_path,
        index_name=index_name,
        embeddings=Azure_langchain_client_embedding,
        allow_dangerous_deserialization=True,
    )

    # Azure Blob Storage configuration (placeholder - to be configured in production)
    # These values should be loaded from environment variables in production
    AZURE_STORAGE_CONNECTION_STRING = ""
    AZURE_STORAGE_CONTAINER_NAME = "isecmapper-documents"

    # Flag to determine whether to use Azure Blob Storage or local storage
    USE_AZURE_BLOB_STORAGE = False  # Set to True in production


configs = Configs()
