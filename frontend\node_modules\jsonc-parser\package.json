{"name": "jsonc-parser", "version": "3.2.1", "description": "Scanner and parser for JSON with comments.", "main": "./lib/umd/main.js", "typings": "./lib/umd/main.d.ts", "module": "./lib/esm/main.js", "author": "Microsoft Corporation", "repository": {"type": "git", "url": "https://github.com/microsoft/node-jsonc-parser"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/node-jsonc-parser/issues"}, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.3.3", "@types/node": "^16.x", "@types/mocha": "^10.0.6", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "eslint": "^8.56.0", "rimraf": "^5.0.5"}, "scripts": {"prepack": "npm run clean && npm run compile-esm && npm run test && npm run remove-sourcemap-refs", "compile": "tsc -p ./src && npm run lint", "compile-esm": "tsc -p ./src/tsconfig.esm.json", "remove-sourcemap-refs": "node ./build/remove-sourcemap-refs.js", "clean": "<PERSON><PERSON><PERSON> lib", "watch": "tsc -w -p ./src", "test": "npm run compile && mocha ./lib/umd/test", "lint": "eslint src/**/*.ts"}}