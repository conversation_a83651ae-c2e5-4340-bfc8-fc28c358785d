{"name": "@angular-devkit/build-angular", "version": "18.0.4", "description": "Angular Webpack Build Facade", "main": "src/index.js", "typings": "src/index.d.ts", "builders": "builders.json", "dependencies": {"@ampproject/remapping": "2.3.0", "@angular/build": "18.0.4", "@angular-devkit/architect": "0.1800.4", "@angular-devkit/build-webpack": "0.1800.4", "@angular-devkit/core": "18.0.4", "@babel/core": "7.24.5", "@babel/generator": "7.24.5", "@babel/helper-annotate-as-pure": "7.22.5", "@babel/helper-split-export-declaration": "7.24.5", "@babel/plugin-transform-async-generator-functions": "7.24.3", "@babel/plugin-transform-async-to-generator": "7.24.1", "@babel/plugin-transform-runtime": "7.24.3", "@babel/preset-env": "7.24.5", "@babel/runtime": "7.24.5", "@discoveryjs/json-ext": "0.5.7", "@ngtools/webpack": "18.0.4", "@vitejs/plugin-basic-ssl": "1.1.0", "ansi-colors": "4.1.3", "autoprefixer": "10.4.19", "babel-loader": "9.1.3", "babel-plugin-istanbul": "6.1.1", "browserslist": "^4.21.5", "copy-webpack-plugin": "11.0.0", "critters": "0.0.22", "css-loader": "7.1.1", "esbuild-wasm": "0.21.3", "fast-glob": "3.3.2", "https-proxy-agent": "7.0.4", "http-proxy-middleware": "3.0.0", "inquirer": "9.2.22", "jsonc-parser": "3.2.1", "karma-source-map-support": "1.4.0", "less": "4.2.0", "less-loader": "12.2.0", "license-webpack-plugin": "4.0.2", "loader-utils": "3.2.1", "magic-string": "0.30.10", "mini-css-extract-plugin": "2.9.0", "mrmime": "2.0.0", "open": "8.4.2", "ora": "5.4.1", "parse5-html-rewriting-stream": "7.0.0", "picomatch": "4.0.2", "piscina": "4.5.0", "postcss": "8.4.38", "postcss-loader": "8.1.1", "resolve-url-loader": "5.0.0", "rxjs": "7.8.1", "sass": "1.77.2", "sass-loader": "14.2.1", "semver": "7.6.2", "source-map-loader": "5.0.0", "source-map-support": "0.5.21", "terser": "5.31.0", "tree-kill": "1.2.2", "tslib": "2.6.2", "undici": "6.18.0", "vite": "5.2.11", "watchpack": "2.4.1", "webpack": "5.91.0", "webpack-dev-middleware": "7.2.1", "webpack-dev-server": "5.0.4", "webpack-merge": "5.10.0", "webpack-subresource-integrity": "5.1.0"}, "optionalDependencies": {"esbuild": "0.21.3"}, "peerDependencies": {"@angular/compiler-cli": "^18.0.0", "@angular/localize": "^18.0.0", "@angular/platform-server": "^18.0.0", "@angular/service-worker": "^18.0.0", "@web/test-runner": "^0.18.0", "browser-sync": "^3.0.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "karma": "^6.3.0", "ng-packagr": "^18.0.0", "protractor": "^7.0.0", "tailwindcss": "^2.0.0 || ^3.0.0", "typescript": ">=5.4 <5.5"}, "peerDependenciesMeta": {"@angular/localize": {"optional": true}, "@angular/platform-server": {"optional": true}, "@angular/service-worker": {"optional": true}, "@web/test-runner": {"optional": true}, "browser-sync": {"optional": true}, "jest": {"optional": true}, "jest-environment-jsdom": {"optional": true}, "karma": {"optional": true}, "ng-packagr": {"optional": true}, "protractor": {"optional": true}, "tailwindcss": {"optional": true}}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli"}