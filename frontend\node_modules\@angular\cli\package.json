{"name": "@angular/cli", "version": "18.0.4", "description": "CLI tool for Angular", "main": "lib/cli/index.js", "bin": {"ng": "./bin/ng.js"}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "angular-cli", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli", "dependencies": {"@angular-devkit/architect": "0.1800.4", "@angular-devkit/core": "18.0.4", "@angular-devkit/schematics": "18.0.4", "@schematics/angular": "18.0.4", "@yarnpkg/lockfile": "1.1.0", "ansi-colors": "4.1.3", "ini": "4.1.2", "inquirer": "9.2.22", "jsonc-parser": "3.2.1", "npm-package-arg": "11.0.2", "npm-pick-manifest": "9.0.1", "ora": "5.4.1", "pacote": "18.0.6", "resolve": "1.22.8", "semver": "7.6.2", "symbol-observable": "4.0.0", "yargs": "17.7.2"}, "ng-update": {"migrations": "@schematics/angular/migrations/migration-collection.json", "packageGroup": {"@angular/cli": "18.0.4", "@angular/build": "18.0.4", "@angular/ssr": "18.0.4", "@angular-devkit/architect": "0.1800.4", "@angular-devkit/build-angular": "18.0.4", "@angular-devkit/build-webpack": "0.1800.4", "@angular-devkit/core": "18.0.4", "@angular-devkit/schematics": "18.0.4"}}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}