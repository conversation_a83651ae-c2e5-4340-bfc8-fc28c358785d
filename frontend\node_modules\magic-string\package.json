{"name": "magic-string", "version": "0.30.10", "packageManager": "pnpm@9.0.1", "description": "Modify strings, generate sourcemaps", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "repository": "https://github.com/rich-harris/magic-string", "license": "MIT", "author": "<PERSON>", "main": "./dist/magic-string.cjs.js", "module": "./dist/magic-string.es.mjs", "jsnext:main": "./dist/magic-string.es.mjs", "types": "./dist/magic-string.cjs.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}}, "files": ["dist/*", "index.d.ts", "README.md"], "scripts": {"build": "rollup -c", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "lint": "eslint src test && publint", "lint:fix": "eslint src test --fix", "prepare": "npm run build", "prepublishOnly": "npm run lint && rm -rf dist && npm test", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "pretest": "npm run build", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "watch": "rollup -cw"}, "devDependencies": {"@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "benchmark": "^2.1.4", "bumpp": "^9.4.0", "conventional-changelog-cli": "^3.0.0", "eslint": "^8.57.0", "mocha": "^10.4.0", "prettier": "^3.2.5", "publint": "^0.2.7", "rollup": "^3.29.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21"}, "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}}