{"version": "6.0.1", "name": "ng2-charts", "description": "Reactive, responsive, beautiful charts for Angular based on Chart.js", "peerDependencies": {"@angular/platform-browser": ">=17.0.0", "@angular/common": ">=17.0.0", "@angular/core": ">=17.0.0", "@angular/cdk": ">=17.0.0", "chart.js": "^3.4.0 || ^4.0.0", "rxjs": "^6.5.3 || ^7.4.0"}, "dependencies": {"lodash-es": "^4.17.15", "tslib": "^2.3.0"}, "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "", "url": "https://github.com/santam85"}, {"name": "Aviad <PERSON>les", "email": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/valor-software/ng2-charts.git"}, "schematics": "./schematics/src/collection.json", "ng-add": {"save": "dependencies"}, "keywords": ["chart.js", "angular"], "bugs": {"url": "https://github.com/valor-software/ng2-charts/issues"}, "homepage": "https://github.com/valor-software/ng2-charts#readme", "module": "fesm2022/ng2-charts.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/ng2-charts.mjs", "esm": "./esm2022/ng2-charts.mjs", "default": "./fesm2022/ng2-charts.mjs"}}, "sideEffects": false}