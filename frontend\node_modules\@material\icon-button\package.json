{"name": "@material/icon-button", "description": "The Material Components for the web icon button component", "version": "15.0.0-canary.7f224ddd4.0", "license": "MIT", "main": "dist/mdc.iconButton.js", "module": "index.js", "sideEffects": false, "keywords": ["material components", "material design", "button", "icon button", "icon toggle"], "repository": {"type": "git", "url": "https://github.com/material-components/material-components-web.git", "directory": "packages/mdc-icon-button"}, "dependencies": {"@material/base": "15.0.0-canary.7f224ddd4.0", "@material/density": "15.0.0-canary.7f224ddd4.0", "@material/dom": "15.0.0-canary.7f224ddd4.0", "@material/elevation": "15.0.0-canary.7f224ddd4.0", "@material/feature-targeting": "15.0.0-canary.7f224ddd4.0", "@material/focus-ring": "15.0.0-canary.7f224ddd4.0", "@material/ripple": "15.0.0-canary.7f224ddd4.0", "@material/rtl": "15.0.0-canary.7f224ddd4.0", "@material/theme": "15.0.0-canary.7f224ddd4.0", "@material/touch-target": "15.0.0-canary.7f224ddd4.0", "tslib": "^2.1.0"}, "publishConfig": {"access": "public"}, "gitHead": "ff8ff3ce455054f1532d1ae0983f40634a524886"}